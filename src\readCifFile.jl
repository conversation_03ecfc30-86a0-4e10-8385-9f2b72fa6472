using CrystalInfoFramework, FilePaths, Crystalline

struct Structure
  symmetry::Vector{SymOperation{3}}
  labels::Vector{String}
  fract_xs::Vector{String}
  fract_ys::Vector{String}
  fract_zs::Vector{String}
end


# function to read cif file and return a Structure
function read_cif_file(filename)::Structure
  cif_file = Cif(filename)
  my_block = first(cif_file).second

  possible_symmetry_keys = [
    "_space_group_symop_operation_xyz",
    "_symmetry_equiv.pos_as_xyz",
    "_symmetry_equiv_pos_as_xyz",
  ]

  local symmetries_from_cif_file::Vector{String}

  for key in possible_symmetry_keys
    if haskey(my_block, key)
      symmetries_from_cif_file = my_block[key]
      break
    end
  end

  if !@isdefined(symmetries_from_cif_file)
    error("No symmetry operations found in file $filename")
  end

  return Structure(
    transform_symmetries(symmetries_from_cif_file),
    my_block["_atom_site_label"],
    my_block["_atom_site_fract_x"],
    my_block["_atom_site_fract_y"],
    my_block["_atom_site_fract_z"],)
end


function transform_symmetries(symmetries::Vector{String})::Vector{SymOperation{3}}
  symmetryOperations = Vector{SymOperation{3}}(undef, length(symmetries))

  for (i, symmetry) in enumerate(symmetries)
    symmetryOperations[i] = SymOperation{3}(parse_symmetry(symmetry))
  end

  return symmetryOperations
end

function parse_symmetry(symmetry::String)::String
  parts = split(symmetry, ',')
  new_parts = Vector{String}(undef, length(parts))

  for (i, part) in enumerate(parts)
    new_parts[i] = parse_symmetry_part(String(strip(part)))
  end

  return join(new_parts, ',')
end

function parse_symmetry_part(part::String)::String
  if !contains(part, '/')
    return part # Simple case: no fraction
  end

  # Strategy: Parse the expression into terms, separate fractions from variables,
  # then reconstruct in the desired order (variables first, then fractions)

  # Handle simple cases first (already in correct format)
  if match(r"^[+-]?[xyz]([+-]\d+/\d+)*$", part) !== nothing
    return part
  end

  # For complex cases, we need to parse and reorder
  # Examples to handle:
  # "1/2+x" -> "x+1/2"
  # "1/3-x+y" -> "-x+y+1/3"
  # "2/3+x-y" -> "x-y+2/3"

  # Split into tokens (variables, operators, fractions)
  tokens = parse_expression_tokens(part)

  # Separate variable terms and fraction terms
  variable_terms = String[]
  fraction_terms = String[]

  i = 1
  current_sign = "+"

  while i <= length(tokens)
    token = tokens[i]

    if token == "+" || token == "-"
      current_sign = token
      i += 1
    elseif contains(token, "/")
      # This is a fraction
      if current_sign == "-"
        push!(fraction_terms, "-$token")
      else
        push!(fraction_terms, "+$token")
      end
      current_sign = "+"
      i += 1
    else
      # This is a variable term (could be x, y, z, or compound like x-y)
      # We need to handle compound terms carefully
      term = token

      # Look ahead to see if there are more variables in this term
      j = i + 1
      while j <= length(tokens) && tokens[j] != "/" && !contains(tokens[j], "/")
        if tokens[j] == "+" || tokens[j] == "-"
          term *= tokens[j]
          j += 1
          if j <= length(tokens) && !contains(tokens[j], "/") && tokens[j] != "+" && tokens[j] != "-"
            term *= tokens[j]
            j += 1
          end
        else
          break
        end
      end

      if current_sign == "-"
        if startswith(term, "-")
          # Double negative becomes positive
          term = term[2:end]
          push!(variable_terms, "+$term")
        else
          push!(variable_terms, "-$term")
        end
      else
        if startswith(term, "-")
          push!(variable_terms, term)
        else
          push!(variable_terms, "+$term")
        end
      end

      current_sign = "+"
      i = j
    end
  end

  # Reconstruct the expression: variables first, then fractions
  result = ""

  # Add variable terms
  for (idx, term) in enumerate(variable_terms)
    if idx == 1 && startswith(term, "+")
      result *= term[2:end]  # Remove leading + for first term
    else
      result *= term
    end
  end

  # Add fraction terms
  for term in fraction_terms
    result *= term
  end

  return result
end

function parse_expression_tokens(expr::String)::Vector{String}
  tokens = String[]
  current_token = ""

  for char in expr
    if char == '+' || char == '-'
      if !isempty(current_token)
        push!(tokens, current_token)
        current_token = ""
      end
      push!(tokens, string(char))
    else
      current_token *= char
    end
  end

  if !isempty(current_token)
    push!(tokens, current_token)
  end

  return tokens
end

