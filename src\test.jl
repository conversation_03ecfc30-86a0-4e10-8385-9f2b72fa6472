# using CrystalInfoFramework, FilePaths, Crystalline
include("func.jl")
include("readCifFile.jl")
include("getSymmetryPoints.jl")

filename = p"C:\\Users\\<USER>\\Documents\\Repos\\julia\\crystal-symmetry-seeker\\examples\\2101167.cif"

my_block::Structure = read_cif_file(filename)

symmetry = my_block.symmetry
labels = my_block.labels
fract_xs = my_block.fract_xs
fract_ys = my_block.fract_ys
fract_zs = my_block.fract_zs

symmetry_points = get_symmetry_points(fract_xs, fract_ys, fract_zs)
unique_points = get_all_points(symmetry, symmetry_points)
Unitcell_points = put_into_range(unique_points)
supercell_points = make_supercell(Unitcell_points, 3)

println(supercell_points)

# # function to print all symmetries
# function print_symmetries(symmetries::Vector{SymOperation{3}})
#   for op in symmetries
#     println(op)
#     # println("Rotation: ", op.rotation)
#     # println("Translation: ", op.translation)
#     # println("---")
#   end
# end

# print_symmetries(symmetry)