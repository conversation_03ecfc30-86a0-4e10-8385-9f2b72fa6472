


# function to clean the fraction by removing the parenthesis and transforming the numbers contained in the parenthesis to continue ad infinitum
function clean_fraction_parentheses(fraction::String)::String
  # Return early if no parentheses
  if !contains(fraction, "(")
    return fraction
  end

  # Find positions of parentheses
  open_paren = findfirst(isequal('('), fraction)
  close_paren = findfirst(isequal(')'), fraction)

  # Extract numbers inside parentheses
  numbers = fraction[open_paren+1:close_paren-1]

  # Create repeated sequence (10 times should be sufficient for most cases)
  repeated_numbers = repeat(numbers, 10)

  # Replace parenthetical expression with repeated sequence
  return replace(fraction, "(" * numbers * ")" => repeated_numbers)

end

# function to parse a fraction without parenthesis into a float
function parse_fraction(fraction::String)::Float64
  clean_fraction = clean_fraction_parentheses(fraction)
  return parse(Float64, clean_fraction)
end

# function to get all symmetry points grouped by symbol
function get_symmetry_points(fract_xs::Vector{String}, fract_ys::Vector{String}, fract_zs::Vector{String})::Vector{Vector{Float64}}
  symmetry_points::Vector{Vector{Float64}} = []
  for (fract_x, fract_y, fract_z) in zip(fract_xs, fract_ys, fract_zs)

    x::Float64 = parse_fraction(fract_x)
    y::Float64 = parse_fraction(fract_y)
    z::Float64 = parse_fraction(fract_z)

    point::Vector{Float64} = [x, y, z, 1.0]
    push!(symmetry_points, point)
  end
  return symmetry_points
end
