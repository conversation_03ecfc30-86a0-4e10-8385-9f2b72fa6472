using CrystalInfoFramework, FilePaths, Crystalline



# function to iterate over every symmetry to get all possible points in the unitcell. It uses Crystalline.jl
function get_all_points(symmetries::Vector{SymOperation{3}}, points::Vector{Vector{Float64}})::Vector{Vector{Float64}}
  all_points = Vector{Vector{Float64}}()

  for point in points
    for symmetryOp in symmetries
      position::Vector{Float64} = symmetryOp * point
      position = convert(Vector{Float64}, position)
      push!(all_points, position)
    end
  end

  return convert(Vector{Vector{Float64}}, unique(all_points))
end


# function to put into range from 0 to 1
function put_into_range(points::Vector{Vector{Float64}})::Vector{Vector{Float64}}
  @boundscheck @assert length(points) > 0 "points is empty"
  for point in points
    for i in 1:length(point)-1
      if point[i] < 0
        point[i] += 1
      elseif point[i] > 1
        point[i] -= 1
      end
    end
  end

  return points
end

# macro to bound check index range
macro bound_check_index(index, lower, upper)
  quote
    if $index < $lower || $index > $upper
      throw(DomainError($index, "index out of bounds"))
    end
  end
end


# function to make a supercell
function make_supercell(points::Vector{Vector{Float64}}, supercell_size::Int)::Vector{Vector{Float64}}
  supercell_points::Vector{Vector{Float64}} = []

  for point in points
    for i in 0:supercell_size-1
      x = point[1] + i

      for j in 0:supercell_size-1
        y = point[2] + j

        for k in 0:supercell_size-1
          z = point[3] + k

          new_point::Vector{Float64} = [x, y, z, 1.0]
          push!(supercell_points, new_point)

        end
      end
    end
  end

  return supercell_points
end


# function to calculate the inverses of symmetry operations, excluding redundant ones.
# Returns: Array of inverse symmetry operations that don't overlap with existing operations
function get_inverse_symmetry_operations(symmetries::Vector{String})::Crystalline.AbstractOperation{3}
  inverse_symmetries::Vector{Crystalline.AbstractOperation{3}} = []
  for symmetry in symmetries
    op = SymOperation{3}(symmetry)
    inverse_op = inv(op)
    if !(inverse_op in symmetries)
      push!(inverse_symmetries, inverse_op)
    end
  end
  return inverse_symmetries
end

