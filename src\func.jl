using CrystalInfoFramework, FilePaths, Crystalline, Base

struct Structure
  symmetry::Vector{SymOperation{3}}
  labels::Vector{String}
  type_symbols::Vector{String}
  symmetry_multiplicities::Vector{String}
  Wyckoff_symbols::Vector{String}
  fract_xs::Vector{String}
  fract_ys::Vector{String}
  fract_zs::Vector{String}
  occupancies::Vector{String}
  calc_flag::Vector{String}
end

symmetry_pattern = Regex("((\\d\\/\\d)([\\+-]))?(\\w)?(([\\+-])(\\d\\/\\d))?")

function parse_symmetry(symmetry::String)::String
  parts = split(symmetry, ',')
  new_parts = Vector{String}(undef, length(parts))

  for (i, part) in enumerate(parts)
    matched_patterns = match(symmetry_pattern, part)

    if isnothing(matched_patterns)
      error("Symmetry $symmetry does not match pattern $symmetry_pattern")
    end

    captures = matched_patterns.captures
    println(captures)

    if isnothing(captures[1])
      # correct format: "x+1/2" or "x"
      new_parts[i] = part
      continue
    end

    # flipped format "1/2+x"
    letter = captures[5]
    sign = captures[4]
    # if sign == "-"

    # end
    fraction = captures[3]

    new_parts[i] = "$letter$sign$fraction"
  end

  return join(new_parts, ',')
end
# println(parse_symmetry("1/2-x,y,z+1/2"))

function transform_symmetries(symmetries::Vector{String})
  symmetryOperations = Vector{SymOperation{3}}(undef, length(symmetries))

  for (i, symmetry) in enumerate(symmetries)
    op = SymOperation{3}(parse_symmetry(symmetry))
    symmetryOperations[i] = op
  end

  return symmetryOperations
end


# function to read cif file and return a Structure
function read_cif_file(filename)::Structure
  cif_file = Cif(filename)
  my_block = first(cif_file).second

  _symmetry_equiv_pos_as_xyz::Vector{String} = my_block["_symmetry_equiv_pos_as_xyz"]

  return Structure(
    transform_symmetries(_symmetry_equiv_pos_as_xyz),
    my_block["_atom_site_label"],
    my_block["_atom_site_type_symbol"],
    my_block["_atom_site_symmetry_multiplicity"],
    my_block["_atom_site_wyckoff_symbol"],
    my_block["_atom_site_fract_x"],
    my_block["_atom_site_fract_y"],
    my_block["_atom_site_fract_z"],
    my_block["_atom_site_occupancy"],
    my_block["_atom_site_calc_flag"]
  )
end


# function to clean the fraction by removing the parenthesis and transforming the numbers contained in the parenthesis to continue ad infinitum
function clean_fraction_parentheses(fraction::String)::String
  # Return early if no parentheses
  if !contains(fraction, "(")
    return fraction
  end

  # Find positions of parentheses
  open_paren = findfirst(isequal('('), fraction)
  close_paren = findfirst(isequal(')'), fraction)

  # Extract numbers inside parentheses
  numbers = fraction[open_paren+1:close_paren-1]

  # Create repeated sequence (10 times should be sufficient for most cases)
  repeated_numbers = repeat(numbers, 10)

  # Replace parenthetical expression with repeated sequence
  return replace(fraction, "(" * numbers * ")" => repeated_numbers)
end

# function to parse a fraction without parenthesis into a float
function parse_fraction(fraction::String)::Float64
  clean_fraction = clean_fraction_parentheses(fraction)
  return parse(Float64, clean_fraction)
end

# function to get all symmetry points grouped by symbol
function get_symmetry_points(fract_xs::Vector{String}, fract_ys::Vector{String}, fract_zs::Vector{String})::Vector{Vector{Float64}}
  symmetry_points::Vector{Vector{Float64}} = []
  for (fract_x, fract_y, fract_z) in zip(fract_xs, fract_ys, fract_zs)

    x::Float64 = parse_fraction(fract_x)
    y::Float64 = parse_fraction(fract_y)
    z::Float64 = parse_fraction(fract_z)

    point::Vector{Float64} = [x, y, z, 1.0]
    push!(symmetry_points, point)
  end
  return symmetry_points
end


# function to iterate over every symmetry to get all possible points in the unitcell. It uses Crystalline.jl
function get_all_points(symmetries::Vector{SymOperation{3}}, points::Vector{Vector{Float64}})::Vector{Vector{Float64}}
  all_points = Vector{Vector{Float64}}()

  for point in points
    for symmetryOp in symmetries
      position::Vector{Float64} = symmetryOp * point
      position = convert(Vector{Float64}, position)
      push!(all_points, position)
    end
  end

  return convert(Vector{Vector{Float64}}, unique(all_points))
end


# function to put into range from 0 to 1
function put_into_range(points::Vector{Vector{Float64}})::Vector{Vector{Float64}}
  @boundscheck @assert length(points) > 0 "points is empty"
  for point in points
    for i in 1:length(point)-1
      if point[i] < 0
        point[i] += 1
      elseif point[i] > 1
        point[i] -= 1
      end
    end
  end

  return points
end

# macro to bound check index range
macro bound_check_index(index, lower, upper)
  quote
    if $index < $lower || $index > $upper
      throw(DomainError($index, "index out of bounds"))
    end
  end
end


# function to make a supercell
function make_supercell(points::Vector{Vector{Float64}}, supercell_size::Int)::Vector{Vector{Float64}}
  supercell_points::Vector{Vector{Float64}} = []

  for point in points
    for i in 0:supercell_size-1
      x = point[1] + i

      for j in 0:supercell_size-1
        y = point[2] + j

        for k in 0:supercell_size-1
          z = point[3] + k

          new_point::Vector{Float64} = [x, y, z, 1.0]
          push!(supercell_points, new_point)

        end
      end
    end
  end

  return supercell_points
end


# function to calculate the inverses of symmetry operations, excluding redundant ones.
# Returns: Array of inverse symmetry operations that don't overlap with existing operations
function get_inverse_symmetry_operations(symmetries::Vector{String})::Crystalline.AbstractOperation{3}
  inverse_symmetries::Vector{Crystalline.AbstractOperation{3}} = []
  for symmetry in symmetries
    op = SymOperation{3}(symmetry)
    inverse_op = inv(op)
    if !(inverse_op in symmetries)
      push!(inverse_symmetries, inverse_op)
    end
  end
  return inverse_symmetries
end

