include("../src/readCifFile.jl")

using Test

@test parse_symmetry("x,y,z") == "x,y,z"

@test parse_symmetry("-z,-x,-y") == "-z,-x,-y"
@test parse_symmetry("x,-y,-z") == "x,-y,-z"
@test parse_symmetry("x,y,-z") == "x,y,-z"
@test parse_symmetry("x,-y,z") == "x,-y,z"

@test parse_symmetry("x+1/2,y,z") == "x+1/2,y,z"
@test parse_symmetry("1/2+x,y,z") == "x+1/2,y,z"

@test parse_symmetry("x,y+1/2,z") == "x,y+1/2,z"
@test parse_symmetry("x,1/2+y,z") == "x,y+1/2,z"

@test parse_symmetry("x,y,z+1/2") == "x,y,z+1/2"
@test parse_symmetry("x,y,1/2+z") == "x,y,z+1/2"

@test parse_symmetry("x-1/2,y,z") == "x-1/2,y,z"
@test parse_symmetry("x,y-1/2,z") == "x,y-1/2,z"
@test parse_symmetry("x,y,z-1/2") == "x,y,z-1/2"

@test parse_symmetry("1/2-x,y,z") == "-x+1/2,y,z"
@test parse_symmetry("x,1/2-y,z") == "x,-y+1/2,z"
@test parse_symmetry("x,y,1/2-z") == "x,y,-z+1/2"

@test parse_symmetry("x,1/2+y,1/2-z") == "x,y+1/2,-z+1/2"
@test parse_symmetry("x,1/2-y,1/2+z") == "x,-y+1/2,z+1/2"

@test parse_symmetry("-1/2-x,y,z") == "-x-1/2,y,z"
@test parse_symmetry("x,-1/2-y,z") == "x,-y-1/2,z"
@test parse_symmetry("x,y,-1/2-z") == "x,y,-z-1/2"

@test parse_symmetry("-1/2-x,-2/3-y,-3/4-z") == "-x-1/2,-y-2/3,-z-3/4"
@test parse_symmetry("1/2+x,2/3-y,-3/4+z") == "x+1/2,-y+2/3,z-3/4"

@test parse_symmetry("x,x-y,1/2+z") == "x,x-y,z+1/2"
@test parse_symmetry("-x+y,y,1/2+z") == "-x+y,y,z+1/2"
@test parse_symmetry("-x,-x+y,1/2-z") == "-x,-x+y,-z+1/2"
@test parse_symmetry("x-y,-y,1/2-z") == "x-y,-y,-z+1/2"

@test parse_symmetry("y,-x+y,-z") == "y,-x+y,-z"
@test parse_symmetry("-x+y,-x,z") == "-x+y,-x,z"
@test parse_symmetry("-y,x-y,z") == "-y,x-y,z"
@test parse_symmetry("x-y,x,-z") == "x-y,x,-z"

@test parse_symmetry("1/3-x+y,2/3+y,1/6+z") == "-x+y+1/3,y+2/3,z+1/6"
@test parse_symmetry("1/3-x,2/3-x+y,1/6-z") == "-x+1/3,-x+y+2/3,-z+1/6"
@test parse_symmetry("1/3+x,2/3+x-y,1/6+z") == "x+1/3,x-y+2/3,z+1/6"
@test parse_symmetry("2/3+x,1/3+x-y,5/6+z") == "x+2/3,x-y+1/3,z+5/6"
@test parse_symmetry("2/3-x+y,1/3+y,5/6+z") == "-x+y+2/3,y+1/3,z+5/6"
@test parse_symmetry("2/3-x,1/3-x+y,5/6-z") == "-x+2/3,-x+y+1/3,-z+5/6"
